"""Add extended_thinking_mode to ThreadTask

Revision ID: 02a267591fce
Revises: 196abf4b8eb7
Create Date: 2025-05-26 21:22:04.412707

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '02a267591fce'
down_revision: Union[str, None] = '196abf4b8eb7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('thread_tasks', sa.Column('extended_thinking_mode', sa.<PERSON>(), nullable=False, server_default='false'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('thread_tasks', 'extended_thinking_mode')
    # ### end Alembic commands ###
