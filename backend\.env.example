DB_HOST=
DB_PORT=
DB_DATABASE=
DB_USERNAME=
DB_PASSWORD=
DB_CONNECTION_STRING=

JWT_ISS=NeuralAgentBackend
JWT_SECRET=

# Keep Empty for now
REDIS_CONNECTION=

# Needed Only if using bedrock
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
BEDROCK_REGION=us-west-2

# Needed Only if using azure openai
AZURE_OPENAI_ENDPOINT=
AZURE_OPENAI_API_KEY=
OPENAI_API_VERSION=2024-12-01-preview

# Needed Only if using OpenAI API
OPENAI_API_KEY=

# Needed Only if using Anthropic API
ANTHROPIC_API_KEY=

# Needed Only if using Gemini
GOOGLE_API_KEY=

# Needed if using Ollama, customize if needed
OLLAMA_URL=http://127.0.0.1:11434

CLASSIFIER_AGENT_MODEL_TYPE=openai|azure_openai|anthropic|bedrock|ollama|gemini # Select One
CLASSIFIER_AGENT_MODEL_ID=gpt-4.1

TITLE_AGENT_MODEL_TYPE=openai|azure_openai|anthropic|bedrock|ollama|gemini # Select One
TITLE_AGENT_MODEL_ID=gpt-4.1-nano

SUGGESTOR_AGENT_MODEL_TYPE=openai|azure_openai|anthropic|bedrock|ollama|gemini # Select One
SUGGESTOR_AGENT_MODEL_ID=gpt-4.1-mini

PLANNER_AGENT_MODEL_TYPE=openai|azure_openai|anthropic|bedrock|ollama|gemini # Select One
PLANNER_AGENT_MODEL_ID=gpt-4.1

COMPUTER_USE_AGENT_MODEL_TYPE=openai|azure_openai|anthropic|bedrock|ollama|gemini # Select One
COMPUTER_USE_AGENT_MODEL_ID=us.anthropic.claude-sonnet-4-20250514-v1:0

SUMMARIZER_AGENT_MODEL_TYPE=openai|azure_openai|anthropic|bedrock|ollama|gemini # Select One
SUMMARIZER_AGENT_MODEL_ID=gpt-4.1-mini

# Internal use only by Neural for optional screenshot logging during training (off by default).
# This is not used by the open-source app or contributors.
ENABLE_SCREENSHOT_LOGGING_FOR_TRAINING=false
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=


# For tracing, keep false if not needed
LANGCHAIN_TRACING_V2=false
LANGCHAIN_ENDPOINT=
LANGCHAIN_API_KEY=
LANGCHAIN_PROJECT=

# For Google Login
GOOGLE_LOGIN_CLIENT_ID=
GOOGLE_LOGIN_CLIENT_SECRET=
GOOGLE_LOGIN_DESKTOP_REDIRECT_URI=http://127.0.0.1:36478