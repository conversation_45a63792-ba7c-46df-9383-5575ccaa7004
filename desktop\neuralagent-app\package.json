{"name": "neuralagent-app", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.6.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.4", "electron-store": "^10.0.1", "moment": "^2.30.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.0", "react-scripts": "5.0.1", "react-spinners": "^0.15.0", "styled-components": "^6.1.17", "web-vitals": "^2.1.4"}, "homepage": "./", "scripts": {"start": "set PORT=6763 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}