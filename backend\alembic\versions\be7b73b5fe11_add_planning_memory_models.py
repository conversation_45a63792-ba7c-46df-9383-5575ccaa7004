"""Add planning, memory models

Revision ID: be7b73b5fe11
Revises: ba82ab28b52f
Create Date: 2025-04-30 04:01:18.077788

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = 'be7b73b5fe11'
down_revision: Union[str, None] = 'ba82ab28b52f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('thread_task_memory_entries',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('thread_task_id', sa.Integer(), nullable=True),
    sa.Column('text', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['thread_task_id'], ['thread_tasks.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_thread_task_memory_entries_id'), 'thread_task_memory_entries', ['id'], unique=False)
    op.create_table('thread_task_plans',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('thread_task_id', sa.Integer(), nullable=True),
    sa.Column('status', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['thread_task_id'], ['thread_tasks.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_thread_task_plans_id'), 'thread_task_plans', ['id'], unique=False)
    op.create_table('plan_subtasks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('thread_task_plan_id', sa.Integer(), nullable=True),
    sa.Column('subtask_text', sa.Text(), nullable=False),
    sa.Column('subtask_type', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('ordering', sa.Integer(), nullable=False),
    sa.Column('status', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['thread_task_plan_id'], ['thread_task_plans.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_plan_subtasks_id'), 'plan_subtasks', ['id'], unique=False)
    op.add_column('login_sessions', sa.Column('login_session_type', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.add_column('thread_messages', sa.Column('plan_subtask_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'thread_messages', 'plan_subtasks', ['plan_subtask_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'thread_messages', type_='foreignkey')
    op.drop_column('thread_messages', 'plan_subtask_id')
    op.drop_column('login_sessions', 'login_session_type')
    op.drop_index(op.f('ix_plan_subtasks_id'), table_name='plan_subtasks')
    op.drop_table('plan_subtasks')
    op.drop_index(op.f('ix_thread_task_plans_id'), table_name='thread_task_plans')
    op.drop_table('thread_task_plans')
    op.drop_index(op.f('ix_thread_task_memory_entries_id'), table_name='thread_task_memory_entries')
    op.drop_table('thread_task_memory_entries')
    # ### end Alembic commands ###
