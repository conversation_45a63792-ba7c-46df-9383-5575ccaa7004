"""Adding screenshot to thread_messages

Revision ID: 78934dd595c7
Revises: 02a267591fce
Create Date: 2025-07-15 20:03:51.183789

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '78934dd595c7'
down_revision: Union[str, None] = '02a267591fce'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('thread_messages', sa.Column('screenshot', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('thread_messages', 'screenshot')
    # ### end Alembic commands ###
