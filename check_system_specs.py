#!/usr/bin/env python3
"""
系统性能检测脚本 - 检查是否能运行 Ollama 本地 AI 模型
"""

import platform
import subprocess
import sys
import json
import os
import shutil
from pathlib import Path

def get_gpu_info():
    """获取 GPU 信息"""
    gpu_info = []
    
    try:
        # 尝试使用 nvidia-smi 获取 NVIDIA GPU 信息
        result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total,driver_version', 
                               '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if line.strip():
                    parts = line.split(', ')
                    if len(parts) >= 3:
                        gpu_info.append({
                            'type': 'NVIDIA',
                            'name': parts[0].strip(),
                            'memory_mb': int(parts[1].strip()),
                            'driver': parts[2].strip()
                        })
    except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
        pass
    
    # 如果没有找到 NVIDIA GPU，检查是否有其他 GPU
    if not gpu_info:
        try:
            # Windows: 使用 wmic
            if platform.system() == "Windows":
                result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                    for line in lines:
                        name = line.strip()
                        if name and name != 'Name':
                            gpu_info.append({
                                'type': 'Other',
                                'name': name,
                                'memory_mb': 'Unknown',
                                'driver': 'Unknown'
                            })
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            pass
    
    return gpu_info

def get_system_info():
    """获取系统基本信息"""
    return {
        'os': platform.system(),
        'os_version': platform.version(),
        'architecture': platform.machine(),
        'processor': platform.processor(),
        'python_version': platform.python_version()
    }

def get_memory_info():
    """获取内存信息 - Windows 版本"""
    try:
        if platform.system() == "Windows":
            # 使用 wmic 获取内存信息
            result = subprocess.run(['wmic', 'computersystem', 'get', 'TotalPhysicalMemory'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.strip() and line.strip() != 'TotalPhysicalMemory':
                        total_bytes = int(line.strip())
                        total_gb = round(total_bytes / (1024**3), 2)
                        return {
                            'total_gb': total_gb,
                            'available_gb': 'Unknown',
                            'used_percent': 'Unknown'
                        }

        # 备用方法：尝试读取 /proc/meminfo (Linux)
        if os.path.exists('/proc/meminfo'):
            with open('/proc/meminfo', 'r') as f:
                lines = f.readlines()
                mem_total = None
                mem_available = None
                for line in lines:
                    if line.startswith('MemTotal:'):
                        mem_total = int(line.split()[1]) * 1024  # kB to bytes
                    elif line.startswith('MemAvailable:'):
                        mem_available = int(line.split()[1]) * 1024  # kB to bytes

                if mem_total:
                    total_gb = round(mem_total / (1024**3), 2)
                    available_gb = round(mem_available / (1024**3), 2) if mem_available else 'Unknown'
                    return {
                        'total_gb': total_gb,
                        'available_gb': available_gb,
                        'used_percent': 'Unknown'
                    }
    except Exception:
        pass

    return {
        'total_gb': 'Unknown',
        'available_gb': 'Unknown',
        'used_percent': 'Unknown'
    }

def get_disk_info():
    """获取磁盘信息"""
    try:
        if platform.system() == "Windows":
            # 获取当前驱动器的磁盘空间
            drive = os.path.splitdrive(os.getcwd())[0] + '\\'
            total, used, free = shutil.disk_usage(drive)
            return {
                'total_gb': round(total / (1024**3), 2),
                'free_gb': round(free / (1024**3), 2),
                'used_percent': round((used / total) * 100, 2)
            }
        else:
            # Unix-like 系统
            total, used, free = shutil.disk_usage('/')
            return {
                'total_gb': round(total / (1024**3), 2),
                'free_gb': round(free / (1024**3), 2),
                'used_percent': round((used / total) * 100, 2)
            }
    except Exception:
        return {
            'total_gb': 'Unknown',
            'free_gb': 'Unknown',
            'used_percent': 'Unknown'
        }

def get_cpu_info():
    """获取 CPU 信息"""
    try:
        if platform.system() == "Windows":
            # 使用 wmic 获取 CPU 信息
            result = subprocess.run(['wmic', 'cpu', 'get', 'NumberOfCores,NumberOfLogicalProcessors'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                for line in lines:
                    parts = line.strip().split()
                    if len(parts) >= 2:
                        try:
                            logical_cores = int(parts[0])
                            physical_cores = int(parts[1])
                            return {
                                'physical_cores': physical_cores,
                                'logical_cores': logical_cores,
                                'max_frequency': 'Unknown',
                                'current_frequency': 'Unknown'
                            }
                        except ValueError:
                            continue

        # 备用方法
        logical_cores = os.cpu_count()
        return {
            'physical_cores': logical_cores // 2 if logical_cores else 'Unknown',  # 估算
            'logical_cores': logical_cores or 'Unknown',
            'max_frequency': 'Unknown',
            'current_frequency': 'Unknown'
        }
    except Exception:
        return {
            'physical_cores': 'Unknown',
            'logical_cores': 'Unknown',
            'max_frequency': 'Unknown',
            'current_frequency': 'Unknown'
        }

def analyze_ollama_compatibility(specs):
    """分析是否适合运行 Ollama"""
    recommendations = []
    compatibility_score = 0

    # 内存检查
    memory_gb = specs['memory']['total_gb']
    if isinstance(memory_gb, (int, float)):
        if memory_gb >= 16:
            recommendations.append("✅ 内存充足 (16GB+) - 可以运行大型模型")
            compatibility_score += 3
        elif memory_gb >= 8:
            recommendations.append("⚠️ 内存适中 (8-16GB) - 可以运行中小型模型")
            compatibility_score += 2
        else:
            recommendations.append("❌ 内存不足 (<8GB) - 只能运行很小的模型")
            compatibility_score += 0
    else:
        recommendations.append("⚠️ 无法检测内存大小")
        compatibility_score += 1
    
    # GPU 检查
    gpu_info = specs['gpu']
    has_nvidia = any(gpu['type'] == 'NVIDIA' for gpu in gpu_info)
    nvidia_vram = 0
    
    if has_nvidia:
        for gpu in gpu_info:
            if gpu['type'] == 'NVIDIA' and isinstance(gpu['memory_mb'], int):
                nvidia_vram = max(nvidia_vram, gpu['memory_mb'])
        
        if nvidia_vram >= 8000:  # 8GB+
            recommendations.append("✅ NVIDIA GPU 显存充足 (8GB+) - 可以快速运行大型模型")
            compatibility_score += 4
        elif nvidia_vram >= 4000:  # 4GB+
            recommendations.append("⚠️ NVIDIA GPU 显存适中 (4-8GB) - 可以运行中型模型")
            compatibility_score += 3
        else:
            recommendations.append("⚠️ NVIDIA GPU 显存较少 (<4GB) - 只能运行小型模型")
            compatibility_score += 1
    else:
        recommendations.append("⚠️ 没有检测到 NVIDIA GPU - 将使用 CPU 运行（较慢）")
        compatibility_score += 1
    
    # CPU 检查
    cpu_cores = specs['cpu']['physical_cores']
    if isinstance(cpu_cores, (int, float)):
        if cpu_cores >= 8:
            recommendations.append("✅ CPU 核心充足 (8+核) - CPU 推理性能良好")
            compatibility_score += 2
        elif cpu_cores >= 4:
            recommendations.append("⚠️ CPU 核心适中 (4-8核) - CPU 推理性能一般")
            compatibility_score += 1
        else:
            recommendations.append("❌ CPU 核心较少 (<4核) - CPU 推理性能较差")
            compatibility_score += 0
    else:
        recommendations.append("⚠️ 无法检测 CPU 核心数")
        compatibility_score += 1

    # 磁盘空间检查
    free_space = specs['disk']['free_gb']
    if isinstance(free_space, (int, float)):
        if free_space >= 50:
            recommendations.append("✅ 磁盘空间充足 (50GB+) - 可以安装多个模型")
            compatibility_score += 1
        elif free_space >= 20:
            recommendations.append("⚠️ 磁盘空间适中 (20-50GB) - 可以安装几个模型")
            compatibility_score += 1
        else:
            recommendations.append("❌ 磁盘空间不足 (<20GB) - 空间可能不够")
            compatibility_score += 0
    else:
        recommendations.append("⚠️ 无法检测磁盘空间")
        compatibility_score += 1
    
    # 总体评估
    max_score = 10
    percentage = (compatibility_score / max_score) * 100
    
    if percentage >= 80:
        overall = "🎉 优秀 - 非常适合运行 Ollama，可以流畅运行大型模型"
    elif percentage >= 60:
        overall = "👍 良好 - 适合运行 Ollama，推荐中型模型"
    elif percentage >= 40:
        overall = "⚠️ 一般 - 可以运行 Ollama，但建议使用小型模型"
    else:
        overall = "❌ 较差 - 不太适合运行 Ollama，体验可能不佳"
    
    return {
        'score': compatibility_score,
        'max_score': max_score,
        'percentage': round(percentage, 1),
        'overall': overall,
        'recommendations': recommendations
    }

def suggest_models(specs):
    """根据配置推荐合适的模型"""
    memory_gb = specs['memory']['total_gb']
    has_nvidia = any(gpu['type'] == 'NVIDIA' for gpu in specs['gpu'])

    suggestions = []

    if isinstance(memory_gb, (int, float)):
        if memory_gb >= 16 and has_nvidia:
            suggestions.extend([
                "🚀 llama3.1:8b - 高质量通用模型",
                "🚀 qwen2.5:7b - 中文友好的高性能模型",
                "🚀 mistral:7b - 快速响应的模型"
            ])
        elif memory_gb >= 8:
            suggestions.extend([
                "⚡ llama3.1:3b - 轻量级但性能不错",
                "⚡ qwen2.5:3b - 中文优化的轻量模型",
                "⚡ phi3:mini - 微软的小型模型"
            ])
        else:
            suggestions.extend([
                "🐣 tinyllama:1.1b - 超轻量级模型",
                "🐣 qwen2.5:0.5b - 最小的中文模型"
            ])
    else:
        suggestions.extend([
            "⚡ llama3.1:3b - 推荐的轻量级模型",
            "⚡ qwen2.5:3b - 中文优化模型",
            "🐣 tinyllama:1.1b - 备用超轻量模型"
        ])

    return suggestions

def main():
    print("🔍 正在检测系统性能...")
    print("=" * 60)
    
    # 收集系统信息
    specs = {
        'system': get_system_info(),
        'memory': get_memory_info(),
        'disk': get_disk_info(),
        'cpu': get_cpu_info(),
        'gpu': get_gpu_info()
    }
    
    # 显示系统信息
    print(f"🖥️  操作系统: {specs['system']['os']} {specs['system']['architecture']}")
    print(f"🧠 处理器: {specs['system']['processor']}")
    print(f"⚡ CPU 核心: {specs['cpu']['physical_cores']} 物理核心, {specs['cpu']['logical_cores']} 逻辑核心")
    print(f"💾 内存: {specs['memory']['total_gb']} GB (可用: {specs['memory']['available_gb']} GB)")
    print(f"💿 磁盘: {specs['disk']['total_gb']} GB (可用: {specs['disk']['free_gb']} GB)")
    
    print(f"\n🎮 GPU 信息:")
    if specs['gpu']:
        for i, gpu in enumerate(specs['gpu'], 1):
            memory_info = f" ({gpu['memory_mb']} MB)" if isinstance(gpu['memory_mb'], int) else ""
            print(f"   {i}. {gpu['name']}{memory_info}")
    else:
        print("   未检测到独立显卡")
    
    print("\n" + "=" * 60)
    
    # 分析兼容性
    analysis = analyze_ollama_compatibility(specs)
    
    print(f"📊 Ollama 兼容性评估: {analysis['percentage']}% ({analysis['score']}/{analysis['max_score']})")
    print(f"🎯 {analysis['overall']}")
    
    print(f"\n📋 详细建议:")
    for rec in analysis['recommendations']:
        print(f"   {rec}")
    
    # 推荐模型
    print(f"\n🤖 推荐的 Ollama 模型:")
    suggestions = suggest_models(specs)
    for suggestion in suggestions:
        print(f"   {suggestion}")
    
    print(f"\n📚 安装 Ollama:")
    print(f"   1. 访问: https://ollama.ai/")
    print(f"   2. 下载适合你系统的版本")
    print(f"   3. 安装后运行: ollama pull <模型名>")
    print(f"   4. 例如: ollama pull llama3.1:3b")
    
    # 保存结果到文件
    output_file = "system_specs_report.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            'specs': specs,
            'analysis': analysis,
            'suggestions': suggestions
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 详细报告已保存到: {output_file}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 检测已取消")
    except Exception as e:
        print(f"\n❌ 检测过程中出现错误: {e}")
        print("请确保已安装 psutil 库: pip install psutil")
