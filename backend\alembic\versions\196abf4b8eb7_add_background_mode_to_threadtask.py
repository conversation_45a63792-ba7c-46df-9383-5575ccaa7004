"""Add background_mode to ThreadTask

Revision ID: 196abf4b8eb7
Revises: 78727ff782aa
Create Date: 2025-05-13 15:56:58.703141

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '196abf4b8eb7'
down_revision: Union[str, None] = '78727ff782aa'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('thread_tasks', sa.Column('background_mode', sa.<PERSON>(), nullable=False, server_default='false'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('thread_tasks', 'background_mode')
    # ### end Alembic commands ###
