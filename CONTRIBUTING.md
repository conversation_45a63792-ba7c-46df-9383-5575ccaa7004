
# 🤝 Contributing to <PERSON>eural<PERSON><PERSON>

We love your input! Whether it's fixing bugs, improving the docs, suggesting new features, or becoming a maintainer, your contributions are welcome.

## 🧰 How to Contribute

### 1. Fork the Repository

Click the “Fork” button at the top right of this page and clone your fork locally.

```bash
git clone https://github.com/your-username/neuralagent.git
cd neuralagent
```

### 2. Set Up Your Environment

Follow the instructions in the `README.md` for setting up the `backend/` and `desktop/` apps.

### 3. Create a Branch

```bash
git checkout -b your-feature-branch-name
```

### 4. Make Your Changes

- For backend: work in `backend/`
- For frontend (Electron/React): work in `desktop/` and `desktop/neuralagent-app/`

### 5. Commit and Push

```bash
git add .
git commit -m "Your descriptive commit message"
git push origin your-feature-branch-name
```

### 6. Create a Pull Request

Go to your fork on GitHub and open a pull request into `main`. Please describe your changes clearly.

---

## 🧪 Tips for Contributors

- Write clean, maintainable code.
- Keep PRs focused and minimal.
- Test your changes before pushing.
- Follow the existing file and naming structure.

---

## 📦 Reporting Bugs or Suggestions

- Create a new issue and choose the relevant template.
- Add clear steps to reproduce for bugs.
- Be concise and constructive in your suggestions.

---

## 💬 Need Help?

Join our [Discord community](https://discord.gg/eGyW3kPcUs) or visit our [website](https://www.getneuralagent.com) for support and updates.

Happy hacking! 🚀
