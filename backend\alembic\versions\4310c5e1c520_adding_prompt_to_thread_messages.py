"""Adding prompt to thread_messages

Revision ID: 4310c5e1c520
Revises: 78934dd595c7
Create Date: 2025-07-22 14:48:13.315948

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4310c5e1c520'
down_revision: Union[str, None] = '78934dd595c7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('thread_messages', sa.Column('prompt', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('thread_messages', 'prompt')
    # ### end Alembic commands ###
