<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e835e940-9427-4367-b74f-706ab7d43c6a" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/db/models.py" beforeDir="false" afterPath="$PROJECT_DIR$/db/models.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/routers/aiagent/background.py" beforeDir="false" afterPath="$PROJECT_DIR$/routers/aiagent/background.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/routers/aiagent/generic.py" beforeDir="false" afterPath="$PROJECT_DIR$/routers/aiagent/generic.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2vfbxGOWsMxwYp28h0FFL4CFDRD" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;dev.sweep.assistant.data.RecentlyEditedFiles&quot;: &quot;utils\\ai_prompts.py;dependencies\\auth_dependencies.py;routers\\apps\\threads.py;utils\\agentic_tools.py;.env;routers\\aiagent\\background.py;routers\\aiagent\\generic.py;schemas\\threads.py;alembic\\versions\\02a267591fce_add_extended_thinking_mode_to_threadtask.py;db\\models.py&quot;,
    &quot;dev.sweep.assistant.data.RecentlyUsedFiles&quot;: &quot;utils\\ai_helpers.py;utils\\aws_s3.py;utils\\ai_prompts.py;requirements.txt;routers\\apps\\auth.py;schemas\\threads.py;dependencies\\generic_dep.py;dependencies\\auth_dependencies.py;routers\\aiagent\\background.py;.env&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Documents/Projects/NeuralAgent/neuralagent-backend/keys&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;sweep.chatMode&quot;: &quot;Agent&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Documents\Projects\NeuralAgent\neuralagent-backend\keys" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-598b0484d0b5-e2d783800521-com.jetbrains.pycharm.community.sharedIndexes.bundled-PC-251.26927.74" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e835e940-9427-4367-b74f-706ab7d43c6a" name="Changes" comment="" />
      <created>1744539320026</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744539320026</updated>
      <workItem from="1751922062303" duration="23000" />
    </task>
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>