
"""Add memory across tasks flag

Revision ID: 78727ff782aa
Revises: be7b73b5fe11
Create Date: 2025-04-30 22:27:34.317915

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '78727ff782aa'
down_revision: Union[str, None] = 'be7b73b5fe11'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('thread_tasks', sa.Column('needs_memory_from_previous_tasks', sa.<PERSON>(), server_default='False', nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('thread_tasks', 'needs_memory_from_previous_tasks')
    # ### end Alembic commands ###
